body {
  background: transparent;
  margin: 0;
  min-height: 100vh;
  color: #fff;
  font-family: Arial, sans-serif;
  display: none; /* <PERSON><PERSON><PERSON><PERSON> ve výchozím stavu */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

body.show {
  display: flex; /* Zobraz<PERSON> k<PERSON> m<PERSON> třídu 'show' */
}

h1 {
  color: #fff;
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: bold;
  letter-spacing: 2px;
}

.game-area {
  width: 440px;
  height: 240px;
  position: relative;
  background: #000;
  border-radius: 20px;
  box-shadow: 0 8px 40px #000b, 0 0 0 4px #17191d;
  margin-bottom: 18px;
  margin-top: 0;
  overflow: visible;
  z-index: 2;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  touch-action: none;
  user-select: none;
}

.success {
  color: #3ff981;
  font-size: 1.1em;
  margin-top: 10px;
  margin-bottom: 8px;
  text-shadow: 0 0 8px #0f4;
  z-index: 3;
  font-weight: bold;
  letter-spacing: 1px;
}

@media (max-width: 700px) {
  .game-area {
    width: 99vw;
  }
}
