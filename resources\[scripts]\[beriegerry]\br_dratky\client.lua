local isMinigameActive = false
local currentCallback = nil

-- Export pro spuštění minihry
exports('startWirePuzzle', function(callback)
    if isMinigameActive then
        return false -- Minihra už běží
    end
    
    isMinigameActive = true
    currentCallback = callback
    
    -- Zobrazit UI
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "showGame"
    })
    
    return true
end)

-- Funkce pro zobrazení notifikace
function ShowNotification(message, type)
    -- Základní GTA notifikace
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(false, false)

    -- Můžete zde přidat vlastní notifikační systém
    -- Například: exports['mythic_notify']:DoHudText(type, message)
end

-- NUI Callbacks
RegisterNUICallback('gameCompleted', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)

    -- Skrýt UI
    SendNUIMessage({
        action = "closeGame"
    })

    -- Zobrazit notifikaci podle výsledku
    if data.success then
        ShowNotification("~g~Minihra úspěšně dokončena!", "success")
    else
        ShowNotification("~r~Minihra neúspěšná!", "error")
    end

    -- Zavolat callback s výsledkem
    if currentCallback then
        currentCallback(data.success)
        currentCallback = nil
    end

    cb('ok')
end)

RegisterNUICallback('closeGame', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)

    -- Skrýt UI
    SendNUIMessage({
        action = "closeGame"
    })

    -- Zobrazit notifikaci o zrušení
    ShowNotification("~o~Minihra zrušena!", "warning")

    -- Zavolat callback s neúspěchem
    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end

    cb('ok')
end)

-- Dev command
RegisterCommand('dev-dratky', function()
    exports['br_dratky']:startWirePuzzle(function(success)
        -- Notifikace se zobrazí automaticky v NUI callbacku
        print("^2[br_dratky] Dev test dokončen - výsledek: " .. tostring(success) .. "^0")
    end)
end, false)

-- Zavření UI při stisknutí ESC
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isMinigameActive then
            if IsControlJustPressed(0, 322) then -- ESC key
                SendNUIMessage({
                    action = "closeGame"
                })
            end
        else
            Citizen.Wait(500)
        end
    end
end)
