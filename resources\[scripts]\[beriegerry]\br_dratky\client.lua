local isMinigameActive = false
local currentCallback = nil

-- Export pro spuštění minihry
exports('startWirePuzzle', function(callback)
    if isMinigameActive then
        return false -- Minihra už běží
    end
    
    isMinigameActive = true
    currentCallback = callback
    
    -- Zobrazit UI
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "showGame"
    })
    
    return true
end)

-- NUI Callbacks
RegisterNUICallback('gameCompleted', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    -- Zavolat callback s výsledkem
    if currentCallback then
        currentCallback(data.success)
        currentCallback = nil
    end
    
    cb('ok')
end)

RegisterNUICallback('closeGame', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    -- Zavolat callback s neúspěchem
    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end
    
    cb('ok')
end)

-- Dev command
RegisterCommand('dev-dratky', function()
    exports['br_dratky']:startWirePuzzle(function(success)
        if success then
            print("^2[br_dratky] Minihra úspěšně dokončena!^0")
        else
            print("^1[br_dratky] Minihra neúspěšná nebo zrušená!^0")
        end
    end)
end, false)

-- Zavření UI při stisknutí ESC
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isMinigameActive then
            if IsControlJustPressed(0, 322) then -- ESC key
                SendNUIMessage({
                    action = "closeGame"
                })
            end
        else
            Citizen.Wait(500)
        end
    end
end)
