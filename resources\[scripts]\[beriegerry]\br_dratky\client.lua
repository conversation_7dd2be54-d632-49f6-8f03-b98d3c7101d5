local isMinigameActive = false
local currentCallback = nil

exports('startWirePuzzle', function(callback)
    if isMinigameActive then
        return false
    end
    
    isMinigameActive = true
    currentCallback = callback
    
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "showGame"
    })
    
    return true
end)

function ShowNotification(title, message, time, type, playSound)
    exports['okokNotify']:<PERSON><PERSON>(title, message, time, type, playSound)
end

RegisterNUICallback('gameCompleted', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)

    SendNUIMessage({
        action = "closeGame"
    })

    if data.success then
        ShowNotification("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> byly správně propojeny!", 3000, "success", true)
    else
        ShowNotification("Zkrat", "Špatné propojení způsobilo zkrat!", 3000, "error", true)
    end

    if currentCallback then
        currentCallback(data.success)
        currentCallback = nil
    end

    cb('ok')
end)

RegisterNUICallback('closeGame', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)

    SendNUIMessage({
        action = "closeGame"
    })

    ShowNotification("<PERSON><PERSON><PERSON><PERSON>", "Minihra byla přerušena", 2000, "warning", false)

    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end

    cb('ok')
end)

RegisterCommand('dev-dratky', function()
    exports['br_dratky']:startWirePuzzle(function(success)
        print("^2[br_dratky] Dev test dokončen - výsledek: " .. tostring(success) .. "^0")
    end)
end, false)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isMinigameActive then
            if IsControlJustPressed(0, 322) then
                SendNUIMessage({
                    action = "closeGame"
                })
            end
        else
            Citizen.Wait(500)
        end
    end
end)
