<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>ui</title>
  <style>
    body {
      background: #090a0b;
      margin: 0;
      min-height: 100vh;
      color: #fff;
      font-family: Arial, sans-serif;
      display: flex; flex-direction: column;
      align-items: center; justify-content: center;
      overflow: hidden;
    }
    h1 {
      color: #fff;
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: bold;
      letter-spacing: 2px;
    }
    .game-area {
      width: 440px;
      height: 240px;
      position: relative;
      background: #000;
      border-radius: 20px;
      box-shadow: 0 8px 40px #000b, 0 0 0 4px #17191d;
      margin-bottom: 18px;
      margin-top: 0;
      overflow: visible;
      z-index: 2;
      display: flex;
      align-items: flex-start;
      justify-content: center;
    }
    canvas {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      pointer-events: auto;
      touch-action: none;
      user-select: none;
    }
    .success {
      color: #3ff981;
      font-size: 1.1em;
      margin-top: 10px;
      margin-bottom: 8px;
      text-shadow: 0 0 8px #0f4;
      z-index: 3;
      font-weight: bold;
      letter-spacing: 1px;
    }
    @media (max-width: 700px) {
      .game-area {
        width: 99vw;
      }
    }
  </style>
</head>
<body>
  <div class="game-area" id="game">
    <canvas id="canvas" width="440" height="240"></canvas>
  </div>
  <div id="success" class="success" style="display:none;">Hotovo!</div>

  <script>
    const COLORS = [
      { name: "red", code: "#ff4142", shadow: "#990909" },
      { name: "blue", code: "#37b7ff", shadow: "#2567aa" },
      { name: "yellow", code: "#ffe554", shadow: "#e7b80a" },
      { name: "pink", code: "#ff88ff", shadow: "#b03f8a" },
      { name: "orange", code: "#ffaa2c", shadow: "#a87219" },
    ];
    const wireCount = COLORS.length;

    function shuffle(a) {
      return a.map((x) => x).sort(() => Math.random() - 0.5);
    }
    let left = COLORS;
    let right = shuffle(COLORS);

    const W = 440,
      H = 240;
    function getY(i) {
      return 32 + (i * (H - 64)) / (wireCount - 1);
    }
    const leftStartX = 0,
      leftEndX = 98;
    const rightStartX = W,
      rightEndX = W - 98;
    const frayedLen = 13;

    let connections = Array(wireCount).fill(null);
    let dragging = false,
      dragFrom = null,
      mouse = { x: 0, y: 0 };

    const canvas = document.getElementById("canvas");
    const ctx = canvas.getContext("2d");
    const successMsg = document.getElementById("success");

    function createCablePattern(color) {
      const patCanvas = document.createElement("canvas");
      patCanvas.width = 12;
      patCanvas.height = 6;
      const pctx = patCanvas.getContext("2d");
      pctx.fillStyle = color;
      pctx.fillRect(0, 0, patCanvas.width, patCanvas.height);
      pctx.strokeStyle = "rgba(255,255,255,0.12)";
      pctx.lineWidth = 1.4;
      for (let x = -6; x < patCanvas.width + 6; x += 4) {
        pctx.beginPath();
        pctx.moveTo(x, 0);
        pctx.lineTo(x + 6, patCanvas.height);
        pctx.stroke();
      }
      return ctx.createPattern(patCanvas, "repeat");
    }

    let cablePatterns = {};
    COLORS.forEach(({ code }) => {
      cablePatterns[code] = createCablePattern(code);
    });

    let leftEnds = [];
    let rightEnds = [];

    function draw() {
      ctx.clearRect(0, 0, W, H);

      leftEnds = [];
      rightEnds = [];

      for (let i = 0; i < wireCount; i++) {
        drawWireSegment(
          left[i].code,
          leftStartX,
          getY(i),
          leftEndX,
          getY(i),
          17,
          left[i].shadow,
          true
        );
        drawWireSegment(
          right[i].code,
          rightStartX,
          getY(i),
          rightEndX,
          getY(i),
          17,
          right[i].shadow,
          true
        );
        leftEnds.push({ x: leftEndX, y: getY(i), idx: i });
        rightEnds.push({ x: rightEndX, y: getY(i), idx: i });
      }

      for (let li = 0; li < wireCount; li++) {
        const ri = connections[li];
        if (ri !== null) {
          drawCurvedWire(
            left[li].code,
            leftEndX + 11,
            getY(li),
            rightEndX - 11,
            getY(ri),
            false,
            left[li].shadow,
            true
          );
          drawFrayedEnd(left[li].code, leftEndX, getY(li), "right");
          drawFrayedEnd(right[ri].code, rightEndX, getY(ri), "left");
        }
      }

      if (dragging && dragFrom !== null) {
        drawCurvedWire(
          left[dragFrom].code,
          leftEndX + 11,
          getY(dragFrom),
          mouse.x,
          mouse.y,
          true,
          left[dragFrom].shadow,
          true
        );
        drawFrayedEnd(left[dragFrom].code, leftEndX, getY(dragFrom), "right");
      }

      for (let i = 0; i < wireCount; i++) {
        if (connections[i] === null && !(dragging && dragFrom === i)) {
          drawFrayedEnd(left[i].code, leftEndX, getY(i), "right");
        }
        if (!connections.includes(i)) {
          drawFrayedEnd(right[i].code, rightEndX, getY(i), "left");
        }
      }
    }

    function drawWireSegment(color, x1, y1, x2, y2, width, shadow, useTexture) {
      ctx.save();
      ctx.lineCap = "round";
      ctx.lineWidth = width;
      ctx.shadowColor = shadow;
      ctx.shadowBlur = 8;
      if (useTexture) {
        ctx.strokeStyle = cablePatterns[color];
      } else {
        ctx.strokeStyle = color;
      }
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.stroke();
      ctx.restore();
    }

    function drawCurvedWire(color, x1, y1, x2, y2, isDrag, shadow, useTexture) {
      ctx.save();
      ctx.lineCap = "round";
      ctx.lineWidth = 10;
      ctx.shadowColor = color;
      ctx.shadowBlur = isDrag ? 13 : 8;
      if (useTexture) {
        ctx.strokeStyle = cablePatterns[color];
      } else {
        ctx.strokeStyle = color;
      }
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      const dx = (x2 - x1) * 0.37;
      ctx.bezierCurveTo(x1 + dx, y1 - 18, x2 - dx, y2 + 18, x2, y2);
      ctx.stroke();
      ctx.restore();
    }

    function drawFrayedEnd(color, x, y, direction) {
      const seed = x * 173 + y * 101;
      function srand() {
        let s = (seed + 3) % 1013;
        return function () {
          s = (s * 1093 + 12345) % 86436;
          return (s % 1000) / 1000;
        };
      }
      const rnd = srand();
      const wires = 9;
      for (let i = 0; i < wires; i++) {
        const angle =
          (direction === "right" ? 0 : Math.PI) + (rnd() - 0.5) * 0.9;
        const l = frayedLen + rnd() * 4;
        ctx.save();
        ctx.strokeStyle = "#bf7232";
        ctx.lineWidth = 2 + rnd() * 1.1;
        ctx.shadowColor = "#ffb04c44";
        ctx.shadowBlur = 1.5;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + Math.cos(angle) * l, y + Math.sin(angle) * l);
        ctx.stroke();
        ctx.restore();
      }
      ctx.save();
      ctx.beginPath();
      if (direction === "right") {
        ctx.moveTo(x - 1, y - 8);
        ctx.lineTo(x + 8, y - 8);
        ctx.lineTo(x + 9, y + 8);
        ctx.lineTo(x - 1, y + 8);
      } else {
        ctx.moveTo(x + 1, y - 8);
        ctx.lineTo(x - 8, y - 8);
        ctx.lineTo(x - 9, y + 8);
        ctx.lineTo(x + 1, y + 8);
      }
      ctx.closePath();
      ctx.fillStyle = color;
      ctx.shadowColor = "#000";
      ctx.shadowBlur = 2;
      ctx.fill();
      ctx.restore();
    }

    function distance(x1, y1, x2, y2) {
      return Math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2);
    }

    function getWireEndAt(x, y, side) {
      const arr = side === "left" ? leftEnds : rightEnds;
      for (let i = 0; i < arr.length; i++) {
        const end = arr[i];
        if (side === "left" && connections[end.idx] !== null) continue;
        if (side === "right" && connections.includes(end.idx)) continue;
        if (distance(x, y, end.x, end.y) < 17) {
          return end.idx;
        }
      }
      return null;
    }

    function onMouseDown(e) {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const idx = getWireEndAt(x, y, "left");
      if (idx !== null) {
        dragging = true;
        dragFrom = idx;
        mouse.x = x;
        mouse.y = y;
        draw();
        document.addEventListener("mousemove", onMouseMove);
        document.addEventListener("mouseup", onMouseUp);
      }
    }

    function onMouseMove(e) {
      const rect = canvas.getBoundingClientRect();
      mouse.x = e.clientX - rect.left;
      mouse.y = e.clientY - rect.top;
      draw();
    }

    function onMouseUp(e) {
      if (!dragging || dragFrom === null) {
        dragging = false;
        dragFrom = null;
        draw();
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
        return;
      }
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const idx = getWireEndAt(x, y, "right");
      if (idx !== null && !connections.includes(idx)) {
        connections[dragFrom] = idx;
      }
      dragging = false;
      dragFrom = null;
      draw();
      checkWin();
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    }

    function onTouchStart(e) {
      e.preventDefault();
      const rect = canvas.getBoundingClientRect();
      const t = e.touches[0];
      const x = t.clientX - rect.left;
      const y = t.clientY - rect.top;
      const idx = getWireEndAt(x, y, "left");
      if (idx !== null) {
        dragging = true;
        dragFrom = idx;
        mouse.x = x;
        mouse.y = y;
        draw();
        document.addEventListener("touchmove", onTouchMove, { passive: false });
        document.addEventListener("touchend", onTouchEnd);
      }
    }
    function onTouchMove(e) {
      e.preventDefault();
      const rect = canvas.getBoundingClientRect();
      const t = e.touches[0];
      mouse.x = t.clientX - rect.left;
      mouse.y = t.clientY - rect.top;
      draw();
    }
    function onTouchEnd(e) {
      if (!dragging || dragFrom === null) {
        dragging = false;
        dragFrom = null;
        draw();
        document.removeEventListener("touchmove", onTouchMove);
        document.removeEventListener("touchend", onTouchEnd);
        return;
      }
      const rect = canvas.getBoundingClientRect();
      const touches = e.changedTouches || [];
      for (let j = 0; j < touches.length; j++) {
        const x = touches[j].clientX - rect.left;
        const y = touches[j].clientY - rect.top;
        const idx = getWireEndAt(x, y, "right");
        if (idx !== null && !connections.includes(idx)) {
          connections[dragFrom] = idx;
        }
      }
      dragging = false;
      dragFrom = null;
      draw();
      checkWin();
      document.removeEventListener("touchmove", onTouchMove);
      document.removeEventListener("touchend", onTouchEnd);
    }

    function checkWin() {
      if (connections.every((c) => c !== null)) {
        let ok = true;
        for (let li = 0; li < wireCount; li++)
          if (left[li].name !== right[connections[li]].name) ok = false;
        if (ok) successMsg.style.display = "";
      }
    }

    function resetGame() {
      right = shuffle(COLORS);
      connections = Array(wireCount).fill(null);
      dragging = false;
      dragFrom = null;
      successMsg.style.display = "none";
      draw();
    }

    resetGame();
    canvas.addEventListener("mousedown", onMouseDown);
    canvas.addEventListener("touchstart", onTouchStart, { passive: false });
    window.addEventListener("resize", draw);
    successMsg.onclick = resetGame;
  </script>
</body>
</html>
