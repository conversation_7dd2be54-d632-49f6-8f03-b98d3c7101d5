# 🔌 br_dratky - Wire Puzzle Minigame

Interaktivní minihra pro propojování drá<PERSON>ů s realistickými zvukovými efekty a okamžitým zkratem při chybě.

## 📋 Obsah
- [Instalace](#instalace)
- [Export API](#export-api)
- [Příklady použití](#příklady-použití)
- [Zvukové efekty](#zvukové-efekty)
- [Dev Commands](#dev-commands)
- [Customizace](#customizace)

## 🚀 Instalace

1. Zkopíruj složku `br_dratky` do `resources/[scripts]/[beriegerry]/`
2. Přidej do `server.cfg`:
   ```
   ensure br_dratky
   ```
3. Restart serveru

## 📡 Export API

### `startWirePuzzle(callback, wireCount)`

Spustí minihra propojování drátů s volitelným počtem drátků.

**Parametry:**
- `callback` (function) - <PERSON><PERSON> volaná po dokončení minihry
- `wireCount` (number, optional) - <PERSON><PERSON>et drátků k propojení (5-12, výchozí: 5)

**Callback parametry:**
- `success` (boolean) - `true` pokud byla minihra úspěšně dokončena, `false` při neúspěchu/zrušení

**Návratová hodnota:**
- `boolean` - `true` pokud se minihra spustila, `false` pokud už běží

## 🎮 Příklady použití

### Základní použití
```lua
-- Výchozí obtížnost (5 drátků)
exports['br_dratky']:startWirePuzzle(function(success)
    if success then
        print("Minihra úspěšně dokončena!")
    else
        print("Minihra neúspěšná nebo zrušená!")
    end
end)

-- Střední obtížnost (8 drátků)
exports['br_dratky']:startWirePuzzle(function(success)
    if success then
        print("Těžší minihra dokončena!")
    end
end, 8)

-- Maximální obtížnost (12 drátků)
exports['br_dratky']:startWirePuzzle(function(success)
    if success then
        print("Expert level dokončen!")
    end
end, 12)
```

### Odemykání dveří
```lua
-- Při interakci s elektronickým zámkem
exports['br_dratky']:startWirePuzzle(function(success)
    if success then
        -- Odemknout dveře
        TriggerEvent('doors:unlock', doorId)
        exports['okokNotify']:Alert("Úspěch", "Dveře odemčeny!", 3000, "success", true)
    else
        -- Spustit alarm nebo pokus znovu
        TriggerEvent('security:alarm', playerId)
        exports['okokNotify']:Alert("Chyba", "Bezpečnostní systém aktivován!", 5000, "error", true)
    end
end)
```

### Hackování systému
```lua
-- Při pokusu o hack
local attempts = 0
local maxAttempts = 3

function attemptHack()
    if attempts >= maxAttempts then
        exports['okokNotify']:Alert("Blokováno", "Příliš mnoho pokusů!", 5000, "error", true)
        return
    end
    
    attempts = attempts + 1
    exports['br_dratky']:startWirePuzzle(function(success)
        if success then
            -- Úspěšný hack
            TriggerEvent('hacking:success')
            attempts = 0 -- Reset pokusů
        else
            -- Neúspěšný pokus
            if attempts >= maxAttempts then
                TriggerEvent('hacking:lockout')
            else
                exports['okokNotify']:Alert("Pokus " .. attempts .. "/" .. maxAttempts, "Zkus to znovu", 3000, "warning", false)
            end
        end
    end)
end
```

### Integrace s ESX/QBCore
```lua
-- ESX příklad
ESX.RegisterUsableItem('hacking_device', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    TriggerClientEvent('br_dratky:startHack', source, function(success)
        if success then
            xPlayer.addMoney(math.random(1000, 5000))
        else
            -- Možnost ztráty zařízení při neúspěchu
            if math.random(1, 100) <= 20 then -- 20% šance
                xPlayer.removeInventoryItem('hacking_device', 1)
            end
        end
    end)
end)

-- Client side
RegisterNetEvent('br_dratky:startHack')
AddEventHandler('br_dratky:startHack', function(callback)
    exports['br_dratky']:startWirePuzzle(callback)
end)
```

## 🔊 Zvukové efekty

Minihra obsahuje realistické zvukové efekty:

- **🎵 Tažení drátu** - Jemný click při začátku tažení
- **✅ Správné propojení** - Pozitivní beep při úspěšném propojení
- **⚡ Zkrat** - Dramatický zvuk při špatném propojení (okamžitý)
- **🎉 Úspěch** - Výherní melodie po dokončení
- **🔍 Hover** - Subtilní ping při přiblížení k cíli

### Vlastní zvuky
Můžete nahradit výchozí zvuky vlastními:
- `ui/sounds/short_circuit.wav` - Zvuk zkratu
- `ui/sounds/success_complete.wav` - Zvuk úspěchu

## 🛠️ Dev Commands

### `/dev-dratky [počet_drátků]`
Spustí minihra pro testování s debug výstupem do konzole.

```lua
/dev-dratky        -- 5 drátků (výchozí)
/dev-dratky 8      -- 8 drátků
/dev-dratky 12     -- 12 drátků (maximum)

-- Výstup v konzoli:
-- ^2[br_dratky] Dev test dokončen - výsledek: true (drátky: 8)^0
```

## 🎨 Customizace

### Pozadí
Nahraď černé pozadí vlastním obrázkem:
1. Ulož obrázek jako `ui/background.png` (440x240px)
2. Uprav `ui/style.css`:
   ```css
   .game-area {
       background-image: url('background.png');
       background-size: cover;
       background-position: center;
   }
   ```

### Obtížnost
Minihra podporuje dynamickou obtížnost pomocí parametru `wireCount`:

```lua
-- Lehká (5 drátků) - výchozí
exports['br_dratky']:startWirePuzzle(callback, 5)

-- Střední (7-8 drátků) - doporučeno pro běžné použití
exports['br_dratky']:startWirePuzzle(callback, 8)

-- Těžká (10-12 drátků) - pro experty
exports['br_dratky']:startWirePuzzle(callback, 12)
```

**Dostupné barvy drátků:**
- 🔴 Červená, 🔵 Modrá, 🟡 Žlutá, 🟣 Růžová, 🟠 Oranžová
- 🟢 Zelená, 🟣 Fialová, 🔵 Cyan, 🟢 Lime, 🔵 Indigo
- 🔵 Teal, 🟡 Amber

**Rozsah:** 5-12 drátků (automaticky omezen)

## 🎯 Herní mechaniky

### Úspěch
- Hráč musí propojit všechny dráty podle barev
- Každé správné propojení vydá pozitivní zvuk
- Po dokončení se přehraje výherní melodie

### Neúspěch (Zkrat)
- **Okamžitý zkrat** při propojení špatných barev
- Vizuální efekt blikání
- Dramatický zvuk zkratu
- Automatické zavření minihry

### Zrušení
- Stisknutí ESC zavře minihra
- Vrátí `false` v callback funkci

## 🔧 Troubleshooting

### Minihra se nezobrazuje
- Zkontroluj, že resource běží: `/restart br_dratky`
- Zkontroluj konzoli pro chyby

### Zvuky nefungují
- Zkontroluj, že audio soubory existují v `ui/sounds/`
- Zkontroluj browser konzoli (F12)

### Černá obrazovka po zkratu
- Tento problém byl opraven - canvas filtr se resetuje při novém spuštění

## 📝 Changelog

### v1.0.0
- ✅ Základní minihra s propojováním drátů
- ✅ Okamžitý zkrat při chybě
- ✅ Zvukové efekty
- ✅ okokNotify integrace
- ✅ Export API
- ✅ Dev commands

---

**Autor:** beriegerry  
**Verze:** 1.0.0  
**Licence:** Vlastní použití
